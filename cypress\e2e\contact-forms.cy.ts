describe('Contact Forms Verification', () => {
  const checkCorrectDialogFormVisible = (text: string) => {
    cy.get('[role="dialog"]').should('be.visible')
    cy.get('[role="dialog"]').should('contain.text', text)
    cy.get('body').type('{esc}')
    cy.get('[role="dialog"]').should('not.exist')
  }

  beforeEach(() => {
    cy.visit('http://localhost:3000')
  })

  describe('Hero Section', () => {
    it('should open EmailSubscriptionForm when clicking "Chart your concept!"', () => {
      cy.get('.grid-in-hero').contains('Chart your concept!').click()
      checkCorrectDialogFormVisible('Need a Spark of Inspiration?')
    })

    it('should open CooperationForm when clicking "Drop a line to start!"', () => {
      cy.get('.grid-in-hero').contains('Drop a line to start!').click()
      checkCorrectDialogFormVisible('Ready to Dream Big Together?')
    })
  })

  describe('Carousel Section', () => {
    it('should open CooperationForm in ClientFeedbackCarouselCard when clicking "Drop a line to start!"', () => {
      cy.get('.grid-in-carousel').children().first().contains('Drop a line to start!').click()
      checkCorrectDialogFormVisible('Ready to Dream Big Together?')
    })

    it('should open CooperationForm in ProjectCarouselCard when clicking "Drop a line to start!"', () => {
      cy.get('.grid-in-carousel').children().last().contains('Drop a line to start!').click()
      checkCorrectDialogFormVisible('Ready to Dream Big Together?')
    })

    it('should open EmailSubscriptionForm in ProjectCarouselCard when clicking "Chart your concept!"', () => {
      cy.get('.grid-in-carousel').contains('Chart your concept!').click()
      checkCorrectDialogFormVisible('Need a Spark of Inspiration?')
    })
  })

  describe('Roadblocks Section', () => {
    it('should open CooperationForm when clicking long prompt link', () => {
      cy.get('.grid-in-roadblocks')
        .contains('Write what bugs you most on the road to making your dream a reality here!')
        .click()
      checkCorrectDialogFormVisible('Ready to Dream Big Together?')
    })

    it('should open ChallengeForm when clicking "Add my challenge!"', () => {
      cy.get('.grid-in-roadblocks').contains('Add my challenge!').click()
      checkCorrectDialogFormVisible('We Care About Your Toughest Challenges')
    })

    it('should open CooperationForm when clicking "Drop a line to start!"', () => {
      cy.get('.grid-in-roadblocks').contains('Drop a line to start!').click()
      checkCorrectDialogFormVisible('Ready to Dream Big Together?')
    })
  })

  describe('Ascension Logs Section', () => {
    it('should open CooperationForm when clicking "Drop a line to start!"', () => {
      cy.get('.grid-in-ascension-logs').contains('Drop a line to start!').click()
      checkCorrectDialogFormVisible('Ready to Dream Big Together?')
    })

    it('should open EmailSubscriptionForm when clicking "chart your concept"', () => {
      cy.get('.grid-in-ascension-logs').contains('chart your concept').click()
      checkCorrectDialogFormVisible('Need a Spark of Inspiration?')
    })
  })

  describe('Application Development Intro Section', () => {
    it('should open CooperationForm when clicking "Drop a line to start!"', () => {
      cy.get('.grid-in-application-development-intro').contains('Drop a line to start!').click()
      checkCorrectDialogFormVisible('Ready to Dream Big Together?')
    })
  })

  describe('Plan Section', () => {
    it('should open CooperationForm when clicking "Drop a line to start!"', () => {
      cy.get('.grid-in-cards-plan').contains('Drop a line to start!').click()
      checkCorrectDialogFormVisible('Ready to Dream Big Together?')
    })
  })

  describe('Footer Section', () => {
    it('should open CooperationForm when clicking "Your Story"', () => {
      cy.get('footer').contains('Your Story').click()
      checkCorrectDialogFormVisible('Ready to Dream Big Together?')
    })
  })
})
