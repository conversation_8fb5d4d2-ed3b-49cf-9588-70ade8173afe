import ListItem from '@/app/components/list-items/ListItem'
import ServiceListItem from '../list-items/types/ServiceListItem'
import CarouselCard from '@/app/components/carousel-cards/CarouselCard'
import { CarouselProps } from '@/components/ui/carousel'
import { PageFolder } from '@/app/types/types'

type Props = {
  services: PageFolder[]
  plugins: CarouselProps['plugins']
  setApi: CarouselProps['setApi']
  stopAutoplay: () => void
  startAutoplay: () => void
}

const ServiceCarouselCard = ({ services, plugins, setApi, stopAutoplay, startAutoplay }: Props) => {
  return (
    <CarouselCard
      title="What we love to do:"
      className="w-full h-auto flex-1"
      plugins={plugins}
      setApi={setApi}
      onMouseEnter={stopAutoplay}
      onMouseLeave={startAutoplay}
    >
      {services.flatMap(({ slug: categorySlug, title, description, pages }) => [
        <ListItem key={`category-${categorySlug}`} path={`/services/${categorySlug}`}>
          <ServiceListItem title={title} description={description} />
        </ListItem>,
        ...pages.map(({ slug, title, description }) => (
          <ListItem key={`page-${categorySlug}-${slug}`} path={`/services/${categorySlug}/${slug}`}>
            <ServiceListItem title={title} description={description} />
          </ListItem>
        )),
      ])}
    </CarouselCard>
  )
}

export default ServiceCarouselCard
