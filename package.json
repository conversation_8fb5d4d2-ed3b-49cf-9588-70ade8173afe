{"name": "manystack.com", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --check .", "format:fix": "prettier --write .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:update-snapshots": "jest --updateSnapshot"}, "dependencies": {"@aws-sdk/client-ses": "^3.731.1", "@hookform/resolvers": "^5.1.1", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "15.4.2", "@next/third-parties": "^15.4.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.10", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/canvas-confetti": "^1.9.0", "@types/jest": "^30.0.0", "@types/mdx": "^2.0.13", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dlx": "^0.2.1", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.4", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "next": "15.4.2", "react": "19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "tailwind-merge": "^3.3.1", "usehooks-ts": "^3.1.1", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/typography": "^0.5.16", "@testing-library/dom": "^10.4.0", "@testing-library/user-event": "^14.6.1", "@types/lodash": "^4.17.7", "@types/node": "^24", "@types/nodemailer": "^6.4.17", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "autoprefixer": "^10.4.21", "cypress": "^14.5.2", "eslint": "^9.18.0", "eslint-config-next": "15.4.2", "eslint-config-prettier": "^10.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "postcss": "^8.5.6", "prettier": "^3.5.3", "string-length": "6.0.0", "strip-ansi": "7.1.0", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.5", "typescript": "^5"}, "resolutions": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6", "strip-ansi": "^6.0.1", "string-length": "^4.0.2"}}