'use client'

import CarouselColumn from '@/app/components/CarouselColumn'
import { useRandomCarouselScrollManager } from '@/app/hooks/useRandomCarouselScrollManager'
import { PageFolder, Project, Feedback } from '@/app/types/types'
import ProjectCarouselCard from '@/app/components/carousel-cards/ProjectCarouselCard'
import ServiceCarouselCard from '@/app/components/carousel-cards/ServiceCarouselCard'
import ClientFeedbackCarouselCard from '@/app/components/carousel-cards/ClientFeedbackCarouselCard'
import { cn } from '@/lib/utils'

type Props = {
  services: PageFolder[]
  clientFeedback: Feedback[]
  projects: Project[]
}

const CarouselSection = ({ services, clientFeedback, projects }: Props) => {
  const { plugins, setApi, startAutoplay, stopAutoplay, activeCarouselIndexes } =
    useRandomCarouselScrollManager([services, clientFeedback, projects])

  return (
    <div
      className={cn(
        'grid-in-carousel',
        'max-xl:contents',
        'xl:flex tall:flex-col',
        'xl:sticky top-0',
        'max-w-[50vw] w-fit h-screen gap-x-10 pb-6'
      )}
    >
      <CarouselColumn
        className={cn(
          'max-xl:contents',
          '*:max-w-2xl *:xl:max-w-md tall:h-3/5 mx-auto',
          '*:first:grid-in-service',
          '*:last:grid-in-feedback'
        )}
      >
        <ServiceCarouselCard
          services={services}
          plugins={[plugins[0]]}
          setApi={setApi(0)}
          stopAutoplay={stopAutoplay}
          startAutoplay={startAutoplay}
        />
        <ClientFeedbackCarouselCard
          clientFeedback={clientFeedback}
          plugins={[plugins[1]]}
          setApi={setApi(1)}
          stopAutoplay={stopAutoplay}
          startAutoplay={startAutoplay}
        />
      </CarouselColumn>
      <CarouselColumn
        className={cn(
          'max-xl:contents',
          '*:max-w-2xl *:xl:max-w-md tall:h-2/5 mx-auto',
          '*:first:grid-in-project'
        )}
      >
        <ProjectCarouselCard
          projects={projects}
          plugins={[plugins[2]]}
          setApi={setApi(2)}
          stopAutoplay={stopAutoplay}
          startAutoplay={startAutoplay}
          activeCarouselIndex={activeCarouselIndexes[2]}
        />
      </CarouselColumn>
    </div>
  )
}

export default CarouselSection
